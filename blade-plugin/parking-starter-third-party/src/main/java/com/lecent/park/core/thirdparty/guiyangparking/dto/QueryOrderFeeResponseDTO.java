package com.lecent.park.core.thirdparty.guiyangparking.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 查询停车费用响应DTO
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
@ApiModel(description = "查询停车费用响应")
public class QueryOrderFeeResponseDTO {

    /**
     * 业务返回状态码
     */
    @ApiModelProperty(value = "业务返回状态码", required = true)
    private String code;

    /**
     * 业务返回信息
     */
    @ApiModelProperty(value = "业务返回信息", required = true)
    private String msg;

    /**
     * 业务响应数据
     */
    @ApiModelProperty(value = "业务响应数据", required = true)
    private OrderFeeDataDTO data;

    /**
     * 订单费用数据DTO
     */
    @Data
    @ApiModel(description = "订单费用数据")
    public static class OrderFeeDataDTO {
        /**
         * 车场Id
         */
        @ApiModelProperty(value = "车场Id", required = true)
        private String parkCode;

        /**
         * 本地停车记录号
         */
        @ApiModelProperty(value = "本地停车记录号", required = true)
        private String orderId;

        /**
         * 总金额（分）
         */
        @ApiModelProperty(value = "总金额（分）", required = true)
        private String totalAmount;

        /**
         * 已缴金额（分）
         */
        @ApiModelProperty(value = "已缴金额（分）", required = true)
        private String paidAmount;

        /**
         * 优惠金额（分）
         */
        @ApiModelProperty(value = "优惠金额（分）", required = true)
        private String discountAmount;

        /**
         * 待付金额（分）
         */
        @ApiModelProperty(value = "待付金额（分）", required = true)
        private String unpayAmount;

        /**
         * 停车总时长（秒）
         */
        @ApiModelProperty(value = "停车总时长（秒）", required = true)
        private String parkingTime;

        /**
         * 最后支付时间
         */
        @ApiModelProperty(value = "最后支付时间", required = true, example = "2025-03-01 20:00:00")
        private String paidTime;

        /**
         * 入场时间
         */
        @ApiModelProperty(value = "入场时间", required = true, example = "2025-03-01 20:00:00")
        private String enterTime;

        /**
         * 车牌号
         */
        @ApiModelProperty(value = "车牌号", required = true)
        private String plate;

        /**
         * 车场名称
         */
        @ApiModelProperty(value = "车场名称", required = true)
        private String parkName;

        /**
         * 优惠券信息
         */
        @ApiModelProperty(value = "优惠券信息")
        private List<CouponResultDTO> coupons;

        /**
         * 免费出场时间（分钟）
         */
        @ApiModelProperty(value = "免费出场时间（分钟）", required = true)
        private String freeTime;
    }

    /**
     * 优惠券结果DTO
     */
    @Data
    @ApiModel(description = "优惠券结果")
    public static class CouponResultDTO {
        /**
         * 优惠券ID
         */
        @ApiModelProperty(value = "优惠券ID", required = true)
        private String couponId;

        /**
         * 实际优惠金额（分）
         */
        @ApiModelProperty(value = "实际优惠金额（分）", required = true)
        private String currDiscountPrice;

        /**
         * 实际优惠时间（分）
         */
        @ApiModelProperty(value = "实际优惠时间（分）")
        private String currDiscountTime;
    }
} 